<template>
    <el-container class="node-info-container">
      <!-- Main Content Area -->
      <el-container>
        <el-aside width="220px" class="tree-aside">
          <el-tree
            :data="treeData"
            :props="defaultProps"
            default-expand-all
            :highlight-current="true"
            :default-checked-keys="[5]"
            node-key="id"
          ></el-tree>
        </el-aside>
        <el-main>
          <div class="toolbar">
             <el-row justify="space-between">
              <el-col :span="14">
                  <el-button type="primary" icon="el-icon-plus" size="small" @click="addRow">新增</el-button>
                  <el-button type="primary" icon="el-icon-edit" size="small" @click="updateRow">修改</el-button>
                  <el-button type="primary" size="small" @click="cancelRow">取消修改</el-button>
                  <el-button type="danger" icon="el-icon-delete" size="small" @click="deleteRow">删除</el-button>
                  <el-button type="success" icon="el-icon-check" size="small" @click="saveRow">保存</el-button>
                  <el-button icon="el-icon-download" size="small" @click="download">导出Excel</el-button>
                  <el-button icon="el-icon-upload2" size="small" @click="upload">导入Excel</el-button>
              </el-col>
              <el-col :span="10">
                <el-form :inline="true" size="small" class="search-form">
                  <el-form-item>
                    <el-select v-model="searchType" placeholder="查询指标">
                      <el-option label="节点名称" value="name"></el-option>
                      <el-option label="节点ID" value="id"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-input v-model="searchTerm" placeholder="请输入"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="performSearch">查询</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
             </el-row>
          </div>
          <el-table :data="tableData" border style="width: 100%" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="id" label="节点ID" width="180"></el-table-column>
            <el-table-column prop="name" label="节点名称" width="180"></el-table-column>
            <el-table-column prop="startDate" label="起始日期"></el-table-column>
            <el-table-column prop="endDate" label="退役日期"></el-table-column>
            <el-table-column prop="region" label="节点所属区域"></el-table-column>
          </el-table>
          <div class="pagination-container">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            >
            </el-pagination>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </template>
  
  <script>
  import { ref } from 'vue';
  import * as XLSX from 'xlsx';
  import { saveAs } from 'file-saver';
  
  export default {
    name: 'NodeInfo',
    setup() {
      const treeData = ref([
        {
          id: 1,
          label: '数据维护',
          children: [
            {
              id: 2,
              label: '节点信息',
              children: [
                  { id: 5, label: '节点' }
              ],
            },
            {
              id: 3,
              label: '机组信息',
            },
            {
              id: 4,
              label: '风电信息',
            },
          ],
        },
      ]);
  
      const defaultProps = {
        children: 'children',
        label: 'label',
      };
  
      const tableData = ref([
          { id: '10016115', name: 'QW_HK', startDate: '20090101', endDate: '21000101', region: '外送'},
          { id: '10016114', name: 'QW_AM', startDate: '20090101', endDate: '21000101', region: '外送'},
          { id: '10016113', name: 'GD_YB', startDate: '20090101', endDate: '21000101', region: '广东'},
          { id: '10016112', name: 'GD_YX', startDate: '20090101', endDate: '21000101', region: '广东'},
          { id: '10016111', name: 'GD_YD', startDate: '20090101', endDate: '21000101', region: '广东'},
          { id: '10016110', name: 'GD_ZXB', startDate: '20090101', endDate: '21000101', region: '广东'},
          { id: '10016109', name: 'GD_ZXN', startDate: '20090101', endDate: '21000101', region: '广东'},
          { id: '10016108', name: 'GD_ZDB', startDate: '20090101', endDate: '21000101', region: '广东'},
          { id: '10016107', name: 'GD_ZDN', startDate: '20090101', endDate: '21000101', region: '广东'},
      ]);
  
      const searchType = ref('name');
      const searchTerm = ref('');
      const currentPage = ref(1);
      const pageSize = ref(10);
      const total = ref(tableData.value.length);
      
      const addRow = () => console.log('Add Row');
      const updateRow = () => console.log('Update Row');
      const cancelRow = () => console.log('Cancel Row');
      const deleteRow = () => console.log('Delete Row');
      const saveRow = () => console.log('Save Row');
      const download = () => console.log('Download');
      const upload = () => console.log('Upload');
      const performSearch = () => console.log('Search');
      const resetSearch = () => console.log('Reset');
      const handleSelectionChange = (val) => console.log('Selection Change', val);
      const handleSizeChange = (val) => {
          pageSize.value = val;
      };
      const handleCurrentChange = (val) => {
          currentPage.value = val;
      };

      const handleExport = () => {
        this.$confirm('确定要导出当前表格数据吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
        }).then(() => {
            // Define the header mapping
            const header = {
                name: '节点名称',
                id: '节点编号',
                voltageLevel: '电压等级 (kV)',
                region: '所属区域',
                type: '节点类型',
                remark: '备注'
            };

            // We use this.list which contains the currently displayed data
            const data = this.list.map(item => {
                let row = {};
                for (const key in header) {
                    row[header[key]] = item[key];
                }
                return row;
            });

            const worksheet = XLSX.utils.json_to_sheet(data);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, '节点信息');

            // Set column widths
            const colWidths = Object.keys(header).map(key => {
                return { wch: Math.max(header[key].length, 15) }; // Set a minimum width
            });
            worksheet['!cols'] = colWidths;

            const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
            const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' });
            saveAs(blob, '节点信息.xlsx');

            this.$message({
                type: 'success',
                message: '导出成功!'
            });
        }).catch(() => {
            this.$message({
                type: 'info',
                message: '已取消导出'
            });          
        });
    }
  
  
      return {
        treeData,
        defaultProps,
        tableData,
        searchType,
        searchTerm,
        currentPage,
        pageSize,
        total,
        addRow,
        updateRow,
        cancelRow,
        deleteRow,
        saveRow,
        download,
        upload,
        performSearch,
        resetSearch,
        handleSelectionChange,
        handleSizeChange,
        handleCurrentChange
      };
    },
  };
  </script>
  
  <style scoped>
  .node-info-container {
    height: 100vh;
  }
  .tree-aside {
    border-right: 1px solid #eee;
    padding: 10px;
  }
  .toolbar {
    margin-bottom: 15px;
  }
  .search-form {
      text-align: right;
  }
  .pagination-container {
    margin-top: 15px;
    text-align: right;
  }
  </style>
  