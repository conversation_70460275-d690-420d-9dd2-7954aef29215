<template>
  <el-container style="height: 100vh;">
    <el-header style="background-color: #409EFF; color: white; display: flex; align-items: center; justify-content: space-between;">
      <span>调度运行仿真平台</span>
      <div>
        <el-link href="#" :underline="false" style="color: white; margin-left: 20px;">首页</el-link>
        <el-link href="#" :underline="false" style="color: white; margin-left: 20px;">个人中心</el-link>
        <el-link href="#" :underline="false" style="color: white; margin-left: 20px;">系统配置</el-link>
        <el-link href="#" :underline="false" style="color: white; margin-left: 20px;">退出登录</el-link>
      </div>
    </el-header>
    <el-container>
      <el-aside width="200px">
        <el-menu default-active="1-1" style="height: 100%;" :unique-opened="true">
           <el-submenu index="1">
            <template #title><i class="el-icon-document"></i>基础数据</template>
            <el-menu-item-group>
              <template #title>资源库</template>
              <el-menu-item index="1-1">节点信息</el-menu-item>
            </el-menu-item-group>
          </el-submenu>
          <el-menu-item index="2">
            <i class="el-icon-s-platform"></i>
            <span>运行模拟</span>
          </el-menu-item>
          <el-menu-item index="3">
            <i class="el-icon-s-data"></i>
            <span>结果展示</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      <el-main>
        <node-info />
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import NodeInfo from './components/NodeInfo.vue';

export default {
  name: 'App',
  components: {
    NodeInfo
  }
};
</script>

<style>
body {
  margin: 0;
  font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
}
.el-header {
  position: sticky;
  top: 0;
  z-index: 100;
}
.el-aside {
  height: calc(100vh - 60px);
  overflow-y: auto;
}
</style>