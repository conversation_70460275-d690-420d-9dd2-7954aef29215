### 需求文档

#### 1. 整体布局

采用业界成熟的“上-左-右”布局模式：

* **顶部导航栏 (Header)**：放置全局性操作，如用户中心、系统设置等。
* **左侧菜单栏 (Sidebar)**：作为一级功能导航，清晰展示系统的核心模块。
* **主内容区 (Main Content Area)**：动态展示所选功能模块的详细内容。

#### 2. 功能模块详解

##### 2.1 顶部导航栏

* **Logo/系统名称**: 位于左上角，暂定为“调度运行仿真平台”。
* **全局链接**: 位于右上角，包含 `首页`, `个人中心`, `系统配置`, `退出登录` 四个链接。

##### 2.2 左侧菜单栏

* 设计为可伸缩的侧边栏，以节省屏幕空间。
* 包含以下一级菜单：
  * `基础数据`
  * `运行模拟` (核心功能)
  * `结果展示`
  * `方案对比`
  * `全景网架`
  * `模型库`
  * `资源库`

##### 3.3 主内容区 - `运行模拟` 模块

这是我们要复刻的核心功能区。

* **3.3.1 方案管理**

  * **方案列表**: 以卡片或列表形式展示已创建的方案。每个方案都应清晰地展示其名称，并提供“修改方案”和“删除方案”的操作入口。
  * **添加方案**:
    * 界面上需要有一个醒目的“添加方案”按钮。
    * 点击后，弹出一个模态对话框。
    * 对话框内包含两个表单项：
      * `方案名` (文本输入，必填)
      * `方案描述` (文本域，选填)
    * 对话框提供“完成”和“取消”按钮。
  * **修改方案**:
    * 点击“修改方案”按钮，弹出的模态对话框需要预先填充该方案的现有信息。
  * **删除方案**:
    * 点击“删除方案”按钮，为防止误操作，需要弹出一个确认对话框。
* **3.3.2 运行模拟配置**

  * 选中一个方案后，进入该方案的配置界面。
  * 界面顶部需要明确标识出当前正在配置的方案名称。
  * 提供一个“运行模拟”按钮，用于启动后台计算。
  * 配置项繁多，因此采用 **标签页 (Tabs)** 来组织，分类如下：
    * `基本参数`
    * `运行选项`
    * `电源选项`
    * `调峰选项`
    * `检修选项`
    * `水电运行选项`
    * `新能源运行选项`
    * `优化选项`
  * 每个标签页下是对应的配置表单（具体表单项待后续细化）。
* **3.3.3 进程管理**

  * 用于跟踪和管理模拟任务的执行情况。
  * 使用 **标签页** 分为 `实时进程` 和 `历史进程`。
  * 提供基于“进程方案”名称的 **搜索** 功能和 **重置** 功能。
  * 进程列表需要展示任务的关键信息，如：方案名、状态、开始/结束时间等。
  * 对于“实时进程”，需要有一个进度条来可视化任务的执行进度。
